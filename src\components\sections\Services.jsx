import { services } from '../../constants.jsx';
import SectionHeader from '../ui/SectionHeader';
import ServiceCard from '../ui/ServiceCard';

const Services = () => {
  return (
    <section id="services" className="section-padding bg-primary/30 relative">
      {/* Background Decoration */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-accent/5 to-transparent"></div>
      
      <div className="container-custom relative z-10">
        <SectionHeader 
          title="Nuestros Servicios de Automatización"
          subtitle="Transformamos tu negocio con soluciones de IA personalizadas que impulsan el crecimiento y optimizan la eficiencia."
        />
        
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <ServiceCard 
              key={service.id}
              icon={service.icon}
              title={service.title}
              description={service.description}
              delay={index * 200}
            />
          ))}
        </div>
        
        {/* Bottom Decoration */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center space-x-2 text-text-secondary">
            <div className="w-12 h-px bg-gradient-to-r from-transparent to-accent"></div>
            <span className="text-sm font-medium">Soluciones Personalizadas</span>
            <div className="w-12 h-px bg-gradient-to-l from-transparent to-accent"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
