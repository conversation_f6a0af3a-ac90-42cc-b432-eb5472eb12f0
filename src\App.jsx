
import { useState, useEffect } from 'react';

function App() {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="bg-gray-900 text-white font-['Inter'] min-h-screen">
      {/* Navigation Bar */}
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-gray-900/95 backdrop-blur-sm' : 'bg-transparent'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="text-2xl font-bold text-purple-400">
              Automatia
            </div>
            <div className="hidden md:flex space-x-8">
              <button
                onClick={() => scrollToSection('servicios')}
                className="text-gray-300 hover:text-purple-400 transition-colors duration-200"
              >
                Servicios
              </button>
              <button
                onClick={() => scrollToSection('casos-exito')}
                className="text-gray-300 hover:text-purple-400 transition-colors duration-200"
              >
                Casos de Éxito
              </button>
              <button
                onClick={() => scrollToSection('contacto')}
                className="text-gray-300 hover:text-purple-400 transition-colors duration-200"
              >
                Contacto
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-purple-300 rounded-full animate-pulse delay-1000"></div>
            <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-500 rounded-full animate-pulse delay-500"></div>
            <div className="absolute top-1/2 right-1/4 w-1 h-1 bg-purple-400 rounded-full animate-pulse delay-700"></div>
            <div className="absolute bottom-1/3 right-1/2 w-2 h-2 bg-purple-300 rounded-full animate-pulse delay-300"></div>
          </div>
        </div>

        <div className="relative z-10 text-center max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
            Automatizamos tu éxito con{' '}
            <span className="text-purple-400">Inteligencia Artificial</span>
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8 animate-fade-in-delay">
            Soluciones de IA a medida para restaurantes, clínicas y comercios que quieren liderar el futuro.
          </p>
          <button
            onClick={() => scrollToSection('servicios')}
            className="bg-purple-500 hover:bg-purple-600 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-300 transform hover:scale-105 animate-fade-in-delay-2"
          >
            Descubre Cómo
          </button>
        </div>
      </section>

      {/* Services Section */}
      <section id="servicios" className="py-20 bg-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 text-purple-400">
            Nuestros Servicios de Automatización
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {/* Service Card 1 */}
            <div className="bg-gray-700 p-8 rounded-lg border border-gray-600 hover:border-purple-400 transition-all duration-300 hover:transform hover:scale-105">
              <div className="text-purple-400 mb-4">
                <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Chatbots Inteligentes 24/7</h3>
              <p className="text-gray-300">
                Creamos chatbots que mejoran la atención al cliente, responden preguntas frecuentes y gestionan reservas, funcionando sin descanso.
              </p>
            </div>

            {/* Service Card 2 */}
            <div className="bg-gray-700 p-8 rounded-lg border border-gray-600 hover:border-purple-400 transition-all duration-300 hover:transform hover:scale-105">
              <div className="text-purple-400 mb-4">
                <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Creación de Contenido con IA</h3>
              <p className="text-gray-300">
                Generamos contenido atractivo y relevante para tus redes sociales, manteniendo tu marca activa y captando nuevos clientes.
              </p>
            </div>

            {/* Service Card 3 */}
            <div className="bg-gray-700 p-8 rounded-lg border border-gray-600 hover:border-purple-400 transition-all duration-300 hover:transform hover:scale-105">
              <div className="text-purple-400 mb-4">
                <svg className="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-4 text-white">Automatización de Email Marketing</h3>
              <p className="text-gray-300">
                Diseñamos secuencias de correos automáticas para nutrir a tus leads, recuperar carritos abandonados y fidelizar a tus clientes.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Success Cases Section */}
      <section id="casos-exito" className="py-20 bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-center mb-16 text-purple-400">
            Clientes que ya están en el Futuro
          </h2>

          {/* Case 1 */}
          <div className="mb-20">
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div className="order-2 md:order-1">
                <img
                  src="https://images.unsplash.com/photo-1629909613654-28e377c37b09?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                  alt="Clínica Dental Moderna"
                  className="rounded-lg shadow-2xl w-full h-64 object-cover"
                />
              </div>
              <div className="order-1 md:order-2">
                <h3 className="text-2xl font-bold mb-4 text-white">Clínica Dental Sonrisa Sana</h3>
                <p className="text-gray-300 mb-6 text-lg">
                  Implementamos un chatbot que agenda citas y responde al 80% de las consultas, reduciendo la carga administrativa en un 30% y aumentando la satisfacción del paciente.
                </p>
                <blockquote className="border-l-4 border-purple-400 pl-4 italic text-purple-300">
                  "Automatia nos ha liberado para centrarnos en nuestros pacientes. ¡Un cambio radical!" - Dra. Eva Ruiz
                </blockquote>
              </div>
            </div>
          </div>

          {/* Case 2 */}
          <div>
            <div className="grid md:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-2xl font-bold mb-4 text-white">Restaurante FusiónGourmet</h3>
                <p className="text-gray-300 mb-6 text-lg">
                  Desarrollamos un sistema de IA que genera y programa publicaciones semanales en redes sociales, logrando un aumento del 50% en la interacción y un 15% más de reservas online.
                </p>
                <blockquote className="border-l-4 border-purple-400 pl-4 italic text-purple-300">
                  "Nuestras redes nunca han estado mejor. ¡Y todo en piloto automático!" - Chef Marco Rossi
                </blockquote>
              </div>
              <div>
                <img
                  src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                  alt="Plato Gourmet"
                  className="rounded-lg shadow-2xl w-full h-64 object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contacto" className="py-20 bg-gray-800">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-8 text-purple-400">
            ¿Listo para Automatizar tu Negocio?
          </h2>
          <p className="text-xl text-gray-300 mb-12">
            Hablemos de cómo la IA puede transformar tu empresa. El primer paso hacia el futuro está a un solo clic de distancia.
          </p>
          <a
            href="mailto:<EMAIL>"
            className="inline-block bg-purple-500 hover:bg-purple-600 text-white font-bold py-4 px-12 rounded-lg text-xl transition-all duration-300 transform hover:scale-105"
          >
            Enviar un Email
          </a>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-gray-400">
            © 2025 Automatia. Todos los derechos reservados.
          </p>
        </div>
      </footer>
    </div>
  );
}

export default App;
