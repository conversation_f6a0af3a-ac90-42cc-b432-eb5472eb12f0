const ServiceCard = ({ icon, title, description, delay = 0 }) => {
  return (
    <div
      className="relative bg-primary p-8 rounded-xl border border-white/10 transition-all duration-300 hover:border-accent hover:-translate-y-2 hover:shadow-2xl hover:shadow-accent/20 group animate-fade-in-up"
      style={{ animationDelay: `${delay}ms` }}
    >
      {/* Icon Container */}
      <div className="text-accent mb-6 group-hover:scale-110 transition-transform duration-300 group-hover:animate-subtle-pulse">
        {icon}
      </div>
      
      {/* Title */}
      <h3 className="text-xl font-bold mb-4 text-text-primary group-hover:text-accent transition-colors duration-300">
        {title}
      </h3>
      
      {/* Description */}
      <p className="text-text-secondary leading-relaxed group-hover:text-text-primary transition-colors duration-300">
        {description}
      </p>
      
      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl pointer-events-none"></div>
    </div>
  );
};

export default ServiceCard;
