@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-delay {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  50% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-delay-2 {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  66% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fade-in-delay 2s ease-out forwards;
}

.animate-fade-in-delay-2 {
  animation: fade-in-delay-2 3s ease-out forwards;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}