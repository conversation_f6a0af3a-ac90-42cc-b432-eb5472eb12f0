@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

@layer base {
  body {
    background-color: #0A0A0A;
    color: #F3F4F6;
    font-family: 'Inter', sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-effect {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .gradient-text {
    background: linear-gradient(to right, #A78BFA, #C4B5FD);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }

  .section-padding {
    padding: 5rem 1rem;
  }

  .container-custom {
    max-width: 80rem;
    margin: 0 auto;
  }

  @media (min-width: 640px) {
    .section-padding {
      padding-left: 1.5rem;
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .section-padding {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }
}